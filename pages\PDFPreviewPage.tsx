
import React, { useEffect, useState, useRef } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useCV } from '../contexts/CVContext';
import { CVData } from '../types';
import { generateCVAsPDF } from '../services/pdfService';
import Button from '../components/common/Button';
import { DownloadIcon, ArrowLeftIcon, FileTextIcon } from 'lucide-react';
import toast from 'react-hot-toast';
import CVTemplateForPDF from '../components/PDF/CVTemplateForPDF';
import { useLanguage } from '../contexts/LanguageContext';


const PDFPreviewPage: React.FC = () => {
  const { cvId } = useParams<{ cvId: string }>(); // cvId from URL will be the key
  const navigate = useNavigate();
  const location = useLocation();
  const { getCV } = useCV();
  const [cvData, setCvData] = useState<CVData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { t, getLocalizedDate } = useLanguage();
  // const pdfPreviewRef = useRef<HTMLDivElement>(null); // For html2canvas, not primary Europass

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const isTempPreview = queryParams.get('temp') === 'true';

    if (isTempPreview && cvId) { // cvId here is the temp ID from URL
        const tempDataString = sessionStorage.getItem('previewCVData');
        if (tempDataString) {
            try {
                const parsedData = JSON.parse(tempDataString) as CVData;
                // Ensure the ID in the stored data matches the ID in the URL for consistency
                if (parsedData.id === cvId) {
                    setCvData(parsedData);
                } else {
                    console.error("Preview ID mismatch. URL ID:", cvId, "Stored ID:", parsedData.id);
                    toast.error(t('pdfPreview.tempDataMismatch'));
                    navigate('/'); // Fallback if IDs don't match
                }
            } catch (error) {
                console.error("Error parsing temporary CV data:", error);
                toast.error(t('pdfPreview.tempDataError'));
                navigate('/');
            }
        } else {
            toast.error(t('pdfPreview.tempDataError'));
            navigate('/');
        }
    } else if (cvId) { // Regular preview for a saved CV
      const data = getCV(cvId);
      if (data) {
        setCvData(data);
      } else {
        toast.error(t('cvEditor.notFound')); 
        navigate('/');
      }
    } else { // No cvId provided in URL
        toast.error(t('pdfPreview.notFoundMessage')); 
        navigate('/');
    }
    setIsLoading(false);
  }, [cvId, getCV, navigate, location.search, t]);

  const handleDownloadPDF = async () => {
    if (cvData) {
      try {
        await generateCVAsPDF(cvData, t, getLocalizedDate); 
        toast.success(t('pdfPreview.downloadSuccess'));
        // Optionally, clear sessionStorage for temp data after download,
        // but user might want to download multiple times or keep it for going back to editor.
        // if (new URLSearchParams(location.search).get('temp') === 'true') {
        //     sessionStorage.removeItem('previewCVData');
        // }
      } catch (error) {
        console.error("Error generating PDF:", error);
        toast.error(t('pdfPreview.downloadError'));
      }
    }
  };

  const handleGoBack = () => {
    const isTempPreview = new URLSearchParams(location.search).get('temp') === 'true';
    if (isTempPreview) {
        // If it's a temp preview, it likely came from an editor page (new or existing CV being edited)
        // Navigating to '/' might be safest, or try to reconstruct original editor path if complex.
        // For simplicity, if it's new (original cvId was undefined) or from edit:
        const originalCvIdIfEditing = cvData?.id?.startsWith('temp-') ? undefined : cvData?.id; 
        if(originalCvIdIfEditing && getCV(originalCvIdIfEditing)) { // check if it was an existing cv
             navigate(`/edit/${originalCvIdIfEditing}`);
        } else {
            navigate('/new'); // if it was a new cv
        }
    } else if (cvId && getCV(cvId)) { // It was a preview of a saved CV
        navigate(`/edit/${cvId}`);
    } else { // Fallback
        navigate('/');
    }
  };


  if (isLoading) {
    return <div className="text-center py-20">{t('pdfPreview.loading')}</div>;
  }

  if (!cvData) {
    return (
      <div className="text-center py-20">
        <FileTextIcon size={48} className="mx-auto text-gray-400 mb-4" />
        <h3 className="text-xl font-medium text-gray-700">{t('pdfPreview.notFound')}</h3>
        <p className="text-gray-500 mt-2">{t('pdfPreview.notFoundMessage')}</p>
        <Button 
            variant="outline" 
            onClick={() => navigate('/')} 
            className="mt-6"
            leftIcon={<ArrowLeftIcon size={18}/>}
        >
            {t('pdfPreview.backToDashboard')}
        </Button>
      </div>
    );
  }
  
  const isTempPreview = new URLSearchParams(location.search).get('temp') === 'true';

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-2">
        <Button 
            variant="ghost" 
            onClick={handleGoBack}
            className="text-primary-600 hover:text-primary-700 self-start sm:self-center"
            leftIcon={<ArrowLeftIcon size={18}/>}
        >
           {isTempPreview ? t('pdfPreview.backToEditor') : (cvId && getCV(cvId) ? t('pdfPreview.backToEditor') : t('pdfPreview.backToDashboard'))}
        </Button>
        <h2 className="text-xl sm:text-2xl font-semibold text-gray-800 text-center sm:text-left order-first sm:order-none">
            {t('pdfPreview.title', {cvName: cvData.title})}
        </h2>
        <Button onClick={handleDownloadPDF} variant="primary" leftIcon={<DownloadIcon size={18}/>} className="self-end sm:self-center">
          {t('pdfPreview.downloadPDF')}
        </Button>
      </div>

      {/* This div is for the HTML preview. The actual PDF is generated by jsPDF. */}
      <div id="cv-preview-render-area" className="bg-white p-1 sm:p-4 md:p-6 shadow-xl rounded-lg border border-gray-300 overflow-x-auto">
        {/* CVTemplateForPDF receives t and getLocalizedDate for self-contained localization */}
        <CVTemplateForPDF cvData={cvData} t={t} getLocalizedDate={getLocalizedDate} />
      </div>
    </div>
  );
};

export default PDFPreviewPage;
